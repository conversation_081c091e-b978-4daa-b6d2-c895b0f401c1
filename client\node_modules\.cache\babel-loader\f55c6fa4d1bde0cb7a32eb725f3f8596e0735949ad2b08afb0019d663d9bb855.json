{"ast": null, "code": "import { isScrollable as $cc38e7bd3fc7b213$export$2bb74740c4e19def } from \"./isScrollable.mjs\";\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $a40c673dc9f6d9c7$export$94ed1c92c7beeb22(node, checkForOverflow) {\n  const scrollParents = [];\n  while (node && node !== document.documentElement) {\n    if ((0, $cc38e7bd3fc7b213$export$2bb74740c4e19def)(node, checkForOverflow)) scrollParents.push(node);\n    node = node.parentElement;\n  }\n  return scrollParents;\n}\nexport { $a40c673dc9f6d9c7$export$94ed1c92c7beeb22 as getScrollParents };", "map": {"version": 3, "names": ["$a40c673dc9f6d9c7$export$94ed1c92c7beeb22", "node", "checkForOverflow", "scrollParents", "document", "documentElement", "$cc38e7bd3fc7b213$export$2bb74740c4e19def", "push", "parentElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\faiasel\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\getScrollParents.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollParents(node: Element, checkForOverflow?: boolean): Element[] {\n  const scrollParents: Element[] = [];\n\n  while (node && node !== document.documentElement) {\n    if (isScrollable(node, checkForOverflow)) {\n      scrollParents.push(node);\n    }\n    node = node.parentElement as Element;\n  }\n\n  return scrollParents;\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAcO,SAASA,0CAAiBC,IAAa,EAAEC,gBAA0B;EACxE,MAAMC,aAAA,GAA2B,EAAE;EAEnC,OAAOF,IAAA,IAAQA,IAAA,KAASG,QAAA,CAASC,eAAe,EAAE;IAChD,IAAI,IAAAC,yCAAW,EAAEL,IAAA,EAAMC,gBAAA,GACrBC,aAAA,CAAcI,IAAI,CAACN,IAAA;IAErBA,IAAA,GAAOA,IAAA,CAAKO,aAAa;EAC3B;EAEA,OAAOL,aAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}