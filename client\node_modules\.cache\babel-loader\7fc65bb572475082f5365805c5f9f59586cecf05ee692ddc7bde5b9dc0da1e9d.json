{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */const $65484d02dcb7eb3e$var$DOMPropNames = new Set(['id']);\nconst $65484d02dcb7eb3e$var$labelablePropNames = new Set(['aria-label', 'aria-labelledby', 'aria-describedby', 'aria-details']);\n// See LinkDOMProps in dom.d.ts.\nconst $65484d02dcb7eb3e$var$linkPropNames = new Set(['href', 'hrefLang', 'target', 'rel', 'download', 'ping', 'referrerPolicy']);\nconst $65484d02dcb7eb3e$var$globalAttrs = new Set(['dir', 'lang', 'hidden', 'inert', 'translate']);\nconst $65484d02dcb7eb3e$var$globalEvents = new Set(['onClick', 'onAuxClick', 'onContextMenu', 'onDoubleClick', 'onMouseDown', 'onMouseEnter', 'onMouseLeave', 'onMouseMove', 'onMouseOut', 'onMouseOver', 'onMouseUp', 'onTouchCancel', 'onTouchEnd', 'onTouchMove', 'onTouchStart', 'onPointerDown', 'onPointerMove', 'onPointerUp', 'onPointerCancel', 'onPointerEnter', 'onPointerLeave', 'onPointerOver', 'onPointerOut', 'onGotPointerCapture', 'onLostPointerCapture', 'onScroll', 'onWheel', 'onAnimationStart', 'onAnimationEnd', 'onAnimationIteration', 'onTransitionCancel', 'onTransitionEnd', 'onTransitionRun', 'onTransitionStart']);\nconst $65484d02dcb7eb3e$var$propRe = /^(data-.*)$/;\nfunction $65484d02dcb7eb3e$export$457c3d6518dd4c6f(props, opts = {}) {\n  let {\n    labelable: labelable,\n    isLink: isLink,\n    global: global,\n    events = global,\n    propNames: propNames\n  } = opts;\n  let filteredProps = {};\n  for (const prop in props) if (Object.prototype.hasOwnProperty.call(props, prop) && ($65484d02dcb7eb3e$var$DOMPropNames.has(prop) || labelable && $65484d02dcb7eb3e$var$labelablePropNames.has(prop) || isLink && $65484d02dcb7eb3e$var$linkPropNames.has(prop) || global && $65484d02dcb7eb3e$var$globalAttrs.has(prop) || events && $65484d02dcb7eb3e$var$globalEvents.has(prop) || prop.endsWith('Capture') && $65484d02dcb7eb3e$var$globalEvents.has(prop.slice(0, -7)) || (propNames === null || propNames === void 0 ? void 0 : propNames.has(prop)) || $65484d02dcb7eb3e$var$propRe.test(prop))) filteredProps[prop] = props[prop];\n  return filteredProps;\n}\nexport { $65484d02dcb7eb3e$export$457c3d6518dd4c6f as filterDOMProps };", "map": {"version": 3, "names": ["$65484d02dcb7eb3e$var$DOMPropNames", "Set", "$65484d02dcb7eb3e$var$labelablePropNames", "$65484d02dcb7eb3e$var$linkPropNames", "$65484d02dcb7eb3e$var$globalAttrs", "$65484d02dcb7eb3e$var$globalEvents", "$65484d02dcb7eb3e$var$propRe", "$65484d02dcb7eb3e$export$457c3d6518dd4c6f", "props", "opts", "labelable", "isLink", "global", "events", "propNames", "filteredProps", "prop", "Object", "prototype", "hasOwnProperty", "call", "has", "endsWith", "slice", "test"], "sources": ["C:\\Users\\<USER>\\Desktop\\faiasel\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\filterDOMProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps, GlobalDOMAttributes, LinkDOMProps} from '@react-types/shared';\n\nconst DOMPropNames = new Set([\n  'id'\n]);\n\nconst labelablePropNames = new Set([\n  'aria-label',\n  'aria-labelledby',\n  'aria-describedby',\n  'aria-details'\n]);\n\n// See LinkDOMProps in dom.d.ts.\nconst linkPropNames = new Set([\n  'href',\n  'hrefLang',\n  'target',\n  'rel',\n  'download',\n  'ping',\n  'referrerPolicy'\n]);\n\nconst globalAttrs = new Set([\n  'dir',\n  'lang',\n  'hidden',\n  'inert',\n  'translate'\n]);\n\nconst globalEvents = new Set([\n  'onClick',\n  'onAuxClick',\n  'onContextMenu',\n  'onDoubleClick',\n  'onMouseDown',\n  'onMouseEnter',\n  'onMouseLeave',\n  'onMouseMove',\n  'onMouseOut',\n  'onMouseOver',\n  'onMouseUp',\n  'onTouchCancel',\n  'onTouchEnd',\n  'onTouchMove',\n  'onTouchStart',\n  'onPointerDown',\n  'onPointerMove',\n  'onPointerUp',\n  'onPointerCancel',\n  'onPointerEnter',\n  'onPointerLeave',\n  'onPointerOver',\n  'onPointerOut',\n  'onGotPointerCapture',\n  'onLostPointerCapture',\n  'onScroll',\n  'onWheel',\n  'onAnimationStart',\n  'onAnimationEnd',\n  'onAnimationIteration',\n  'onTransitionCancel',\n  'onTransitionEnd',\n  'onTransitionRun',\n  'onTransitionStart'\n]);\n\ninterface Options {\n  /**\n   * If labelling associated aria properties should be included in the filter.\n   */\n  labelable?: boolean,\n  /** Whether the element is a link and should include DOM props for <a> elements. */\n  isLink?: boolean,\n  /** Whether to include global DOM attributes. */\n  global?: boolean,\n  /** Whether to include DOM events. */\n  events?: boolean,\n  /**\n   * A Set of other property names that should be included in the filter.\n   */\n  propNames?: Set<string>\n}\n\nconst propRe = /^(data-.*)$/;\n\n/**\n * Filters out all props that aren't valid DOM props or defined via override prop obj.\n * @param props - The component props to be filtered.\n * @param opts - Props to override.\n */\nexport function filterDOMProps(props: DOMProps & AriaLabelingProps & LinkDOMProps & GlobalDOMAttributes, opts: Options = {}): DOMProps & AriaLabelingProps & GlobalDOMAttributes {\n  let {labelable, isLink, global, events = global, propNames} = opts;\n  let filteredProps = {};\n\n  for (const prop in props) {\n    if (\n      Object.prototype.hasOwnProperty.call(props, prop) && (\n        DOMPropNames.has(prop) ||\n        (labelable && labelablePropNames.has(prop)) ||\n        (isLink && linkPropNames.has(prop)) ||\n        (global && globalAttrs.has(prop)) ||\n        (events && globalEvents.has(prop) || (prop.endsWith('Capture') && globalEvents.has(prop.slice(0, -7)))) ||\n        propNames?.has(prop) ||\n        propRe.test(prop)\n      )\n    ) {\n      filteredProps[prop] = props[prop];\n    }\n  }\n\n  return filteredProps;\n}\n"], "mappings": "AAAA;;;;;;;;;;GAcA,MAAMA,kCAAA,GAAe,IAAIC,GAAA,CAAI,CAC3B,KACD;AAED,MAAMC,wCAAA,GAAqB,IAAID,GAAA,CAAI,CACjC,cACA,mBACA,oBACA,eACD;AAED;AACA,MAAME,mCAAA,GAAgB,IAAIF,GAAA,CAAI,CAC5B,QACA,YACA,UACA,OACA,YACA,QACA,iBACD;AAED,MAAMG,iCAAA,GAAc,IAAIH,GAAA,CAAI,CAC1B,OACA,QACA,UACA,SACA,YACD;AAED,MAAMI,kCAAA,GAAe,IAAIJ,GAAA,CAAI,CAC3B,WACA,cACA,iBACA,iBACA,eACA,gBACA,gBACA,eACA,cACA,eACA,aACA,iBACA,cACA,eACA,gBACA,iBACA,iBACA,eACA,mBACA,kBACA,kBACA,iBACA,gBACA,uBACA,wBACA,YACA,WACA,oBACA,kBACA,wBACA,sBACA,mBACA,mBACA,oBACD;AAmBD,MAAMK,4BAAA,GAAS;AAOR,SAASC,0CAAeC,KAAwE,EAAEC,IAAA,GAAgB,CAAC,CAAC;EACzH,IAAI;IAAAC,SAAA,EAACA,SAAS;IAAAC,MAAA,EAAEA,MAAM;IAAAC,MAAA,EAAEA,MAAM;IAAEC,MAAA,GAASD,MAAA;IAAAE,SAAA,EAAQA;EAAS,CAAC,GAAGL,IAAA;EAC9D,IAAIM,aAAA,GAAgB,CAAC;EAErB,KAAK,MAAMC,IAAA,IAAQR,KAAA,EACjB,IACES,MAAA,CAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACZ,KAAA,EAAOQ,IAAA,MAC1ChB,kCAAA,CAAaqB,GAAG,CAACL,IAAA,KAChBN,SAAA,IAAaR,wCAAA,CAAmBmB,GAAG,CAACL,IAAA,KACpCL,MAAA,IAAUR,mCAAA,CAAckB,GAAG,CAACL,IAAA,KAC5BJ,MAAA,IAAUR,iCAAA,CAAYiB,GAAG,CAACL,IAAA,KAC1BH,MAAA,IAAUR,kCAAA,CAAagB,GAAG,CAACL,IAAA,KAAUA,IAAA,CAAKM,QAAQ,CAAC,cAAcjB,kCAAA,CAAagB,GAAG,CAACL,IAAA,CAAKO,KAAK,CAAC,GAAG,SACjGT,SAAA,aAAAA,SAAA,uBAAAA,SAAA,CAAWO,GAAG,CAACL,IAAA,MACfV,4BAAA,CAAOkB,IAAI,CAACR,IAAA,CAAI,GAGlBD,aAAa,CAACC,IAAA,CAAK,GAAGR,KAAK,CAACQ,IAAA,CAAK;EAIrC,OAAOD,aAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}