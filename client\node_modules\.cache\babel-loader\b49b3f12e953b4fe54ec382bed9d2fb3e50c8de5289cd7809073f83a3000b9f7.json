{"ast": null, "code": "import { useEffectEvent as $8ae05eaa5c114e9c$export$7f54fc3180508a52 } from \"./useEffectEvent.mjs\";\nimport { useEffect as $8rM3G$useEffect } from \"react\";\n\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction $99facab73266f662$export$5add1d006293d136(ref, initialValue, onReset) {\n  let handleReset = (0, $8ae05eaa5c114e9c$export$7f54fc3180508a52)(() => {\n    if (onReset) onReset(initialValue);\n  });\n  (0, $8rM3G$useEffect)(() => {\n    var _ref_current;\n    let form = ref === null || ref === void 0 ? void 0 : (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.form;\n    form === null || form === void 0 ? void 0 : form.addEventListener('reset', handleReset);\n    return () => {\n      form === null || form === void 0 ? void 0 : form.removeEventListener('reset', handleReset);\n    };\n  }, [ref, handleReset]);\n}\nexport { $99facab73266f662$export$5add1d006293d136 as useFormReset };", "map": {"version": 3, "names": ["$99facab73266f662$export$5add1d006293d136", "ref", "initialValue", "onReset", "handleReset", "$8ae05eaa5c114e9c$export$7f54fc3180508a52", "$8rM3G$useEffect", "_ref_current", "form", "current", "addEventListener", "removeEventListener"], "sources": ["C:\\Users\\<USER>\\Desktop\\faiasel\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useFormReset.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {RefObject} from '@react-types/shared';\nimport {useEffect} from 'react';\nimport {useEffectEvent} from './useEffectEvent';\n\nexport function useFormReset<T>(\n  ref: RefObject<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null> | undefined,\n  initialValue: T,\n  onReset: (value: T) => void\n): void {\n  let handleReset = useEffectEvent(() => {\n    if (onReset) {\n      onReset(initialValue);\n    }\n  });\n\n  useEffect(() => {\n    let form = ref?.current?.form;\n\n    form?.addEventListener('reset', handleReset);\n    return () => {\n      form?.removeEventListener('reset', handleReset);\n    };\n  }, [ref, handleReset]);\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAgBO,SAASA,0CACdC,GAA6F,EAC7FC,YAAe,EACfC,OAA2B;EAE3B,IAAIC,WAAA,GAAc,IAAAC,yCAAa,EAAE;IAC/B,IAAIF,OAAA,EACFA,OAAA,CAAQD,YAAA;EAEZ;EAEA,IAAAI,gBAAQ,EAAE;QACGC,YAAA;IAAX,IAAIC,IAAA,GAAOP,GAAA,aAAAA,GAAA,wBAAAM,YAAA,GAAAN,GAAA,CAAKQ,OAAO,cAAZF,YAAA,uBAAAA,YAAA,CAAcC,IAAI;IAE7BA,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAME,gBAAgB,CAAC,SAASN,WAAA;IAChC,OAAO;MACLI,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAMG,mBAAmB,CAAC,SAASP,WAAA;IACrC;EACF,GAAG,CAACH,GAAA,EAAKG,WAAA,CAAY;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}