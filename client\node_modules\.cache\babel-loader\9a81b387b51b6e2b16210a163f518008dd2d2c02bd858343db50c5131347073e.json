{"ast": null, "code": "import { useRef as $gbmns$useRef, useCallback as $gbmns$useCallback, useMemo as $gbmns$useMemo } from \"react\";\n\n/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $df56164dff5785e2$export$4338b53315abf666(ref) {\n  const objRef = (0, $gbmns$useRef)(null);\n  const cleanupRef = (0, $gbmns$useRef)(undefined);\n  const refEffect = (0, $gbmns$useCallback)(instance => {\n    if (typeof ref === 'function') {\n      const refCallback = ref;\n      const refCleanup = refCallback(instance);\n      return () => {\n        if (typeof refCleanup === 'function') refCleanup();else refCallback(null);\n      };\n    } else if (ref) {\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    }\n  }, [ref]);\n  return (0, $gbmns$useMemo)(() => ({\n    get current() {\n      return objRef.current;\n    },\n    set current(value) {\n      objRef.current = value;\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) cleanupRef.current = refEffect(value);\n    }\n  }), [refEffect]);\n}\nexport { $df56164dff5785e2$export$4338b53315abf666 as useObjectRef };", "map": {"version": 3, "names": ["$df56164dff5785e2$export$4338b53315abf666", "ref", "objRef", "$gbmns$useRef", "cleanupRef", "undefined", "refEffect", "$gbmns$useCallback", "instance", "refC<PERSON><PERSON>", "refCleanup", "current", "$gbmns$useMemo", "value"], "sources": ["C:\\Users\\<USER>\\Desktop\\faiasel\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useObjectRef.ts"], "sourcesContent": ["/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject, useCallback, useMemo, useRef} from 'react';\n\n/**\n * Offers an object ref for a given callback ref or an object ref. Especially\n * helfpul when passing forwarded refs (created using `React.forwardRef`) to\n * React Aria hooks.\n *\n * @param ref The original ref intended to be used.\n * @returns An object ref that updates the given ref.\n * @see https://react.dev/reference/react/forwardRef\n */\nexport function useObjectRef<T>(ref?: ((instance: T | null) => (() => void) | void) | MutableRefObject<T | null> | null): MutableRefObject<T | null> {\n  const objRef: MutableRefObject<T | null> = useRef<T>(null);\n  const cleanupRef: MutableRefObject<(() => void) | void> = useRef(undefined);\n\n  const refEffect = useCallback(\n    (instance: T | null) => {\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return () => {\n          if (typeof refCleanup === 'function') {\n            refCleanup();\n          } else {\n            refCallback(null);\n          }\n        };\n      } else if (ref) {\n        ref.current = instance;\n        return () => {\n          ref.current = null;\n        };\n      }\n    },\n    [ref]\n  );\n\n  return useMemo(\n    () => ({\n      get current() {\n        return objRef.current;\n      },\n      set current(value) {\n        objRef.current = value;\n        if (cleanupRef.current) {\n          cleanupRef.current();\n          cleanupRef.current = undefined;\n        }\n\n        if (value != null) {\n          cleanupRef.current = refEffect(value);\n        }\n      }\n    }),\n    [refEffect]\n  );\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAuBO,SAASA,0CAAgBC,GAAuF;EACrH,MAAMC,MAAA,GAAqC,IAAAC,aAAK,EAAK;EACrD,MAAMC,UAAA,GAAoD,IAAAD,aAAK,EAAEE,SAAA;EAEjE,MAAMC,SAAA,GAAY,IAAAC,kBAAU,EACzBC,QAAA;IACC,IAAI,OAAOP,GAAA,KAAQ,YAAY;MAC7B,MAAMQ,WAAA,GAAcR,GAAA;MACpB,MAAMS,UAAA,GAAaD,WAAA,CAAYD,QAAA;MAC/B,OAAO;QACL,IAAI,OAAOE,UAAA,KAAe,YACxBA,UAAA,QAEAD,WAAA,CAAY;MAEhB;IACF,OAAO,IAAIR,GAAA,EAAK;MACdA,GAAA,CAAIU,OAAO,GAAGH,QAAA;MACd,OAAO;QACLP,GAAA,CAAIU,OAAO,GAAG;MAChB;IACF;EACF,GACA,CAACV,GAAA,CAAI;EAGP,OAAO,IAAAW,cAAM,EACX,OAAO;IACL,IAAID,QAAA,EAAU;MACZ,OAAOT,MAAA,CAAOS,OAAO;IACvB;IACA,IAAIA,QAAQE,KAAA,EAAO;MACjBX,MAAA,CAAOS,OAAO,GAAGE,KAAA;MACjB,IAAIT,UAAA,CAAWO,OAAO,EAAE;QACtBP,UAAA,CAAWO,OAAO;QAClBP,UAAA,CAAWO,OAAO,GAAGN,SAAA;MACvB;MAEA,IAAIQ,KAAA,IAAS,MACXT,UAAA,CAAWO,OAAO,GAAGL,SAAA,CAAUO,KAAA;IAEnC;EACF,IACA,CAACP,SAAA,CAAU;AAEf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}