{"ast": null, "code": "import { useLayoutEffect as $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c } from \"./useLayoutEffect.mjs\";\nimport { useState as $hQ5Hp$useState } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet $ef06256079686ba0$var$descriptionId = 0;\nconst $ef06256079686ba0$var$descriptionNodes = new Map();\nfunction $ef06256079686ba0$export$f8aeda7b10753fa1(description) {\n  let [id, setId] = (0, $hQ5Hp$useState)();\n  (0, $f0a04ccd8dbdd83b$export$e5c5a5f917a5871c)(() => {\n    if (!description) return;\n    let desc = $ef06256079686ba0$var$descriptionNodes.get(description);\n    if (!desc) {\n      let id = `react-aria-description-${$ef06256079686ba0$var$descriptionId++}`;\n      setId(id);\n      let node = document.createElement('div');\n      node.id = id;\n      node.style.display = 'none';\n      node.textContent = description;\n      document.body.appendChild(node);\n      desc = {\n        refCount: 0,\n        element: node\n      };\n      $ef06256079686ba0$var$descriptionNodes.set(description, desc);\n    } else setId(desc.element.id);\n    desc.refCount++;\n    return () => {\n      if (desc && --desc.refCount === 0) {\n        desc.element.remove();\n        $ef06256079686ba0$var$descriptionNodes.delete(description);\n      }\n    };\n  }, [description]);\n  return {\n    'aria-describedby': description ? id : undefined\n  };\n}\nexport { $ef06256079686ba0$export$f8aeda7b10753fa1 as useDescription };", "map": {"version": 3, "names": ["$ef06256079686ba0$var$descriptionId", "$ef06256079686ba0$var$descriptionNodes", "Map", "$ef06256079686ba0$export$f8aeda7b10753fa1", "description", "id", "setId", "$hQ5Hp$useState", "$f0a04ccd8dbdd83b$export$e5c5a5f917a5871c", "desc", "get", "node", "document", "createElement", "style", "display", "textContent", "body", "append<PERSON><PERSON><PERSON>", "refCount", "element", "set", "remove", "delete", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\faiasel\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\useDescription.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps} from '@react-types/shared';\nimport {useLayoutEffect} from './useLayoutEffect';\nimport {useState} from 'react';\n\nlet descriptionId = 0;\nconst descriptionNodes = new Map<string, {refCount: number, element: Element}>();\n\nexport function useDescription(description?: string): AriaLabelingProps {\n  let [id, setId] = useState<string | undefined>();\n\n  useLayoutEffect(() => {\n    if (!description) {\n      return;\n    }\n\n    let desc = descriptionNodes.get(description);\n    if (!desc) {\n      let id = `react-aria-description-${descriptionId++}`;\n      setId(id);\n\n      let node = document.createElement('div');\n      node.id = id;\n      node.style.display = 'none';\n      node.textContent = description;\n      document.body.appendChild(node);\n      desc = {refCount: 0, element: node};\n      descriptionNodes.set(description, desc);\n    } else {\n      setId(desc.element.id);\n    }\n\n    desc.refCount++;\n    return () => {\n      if (desc && --desc.refCount === 0) {\n        desc.element.remove();\n        descriptionNodes.delete(description);\n      }\n    };\n  }, [description]);\n\n  return {\n    'aria-describedby': description ? id : undefined\n  };\n}\n"], "mappings": ";;;AAAA;;;;;;;;;;;;AAgBA,IAAIA,mCAAA,GAAgB;AACpB,MAAMC,sCAAA,GAAmB,IAAIC,GAAA;AAEtB,SAASC,0CAAeC,WAAoB;EACjD,IAAI,CAACC,EAAA,EAAIC,KAAA,CAAM,GAAG,IAAAC,eAAO;EAEzB,IAAAC,yCAAc,EAAE;IACd,IAAI,CAACJ,WAAA,EACH;IAGF,IAAIK,IAAA,GAAOR,sCAAA,CAAiBS,GAAG,CAACN,WAAA;IAChC,IAAI,CAACK,IAAA,EAAM;MACT,IAAIJ,EAAA,GAAK,0BAA0BL,mCAAA,IAAiB;MACpDM,KAAA,CAAMD,EAAA;MAEN,IAAIM,IAAA,GAAOC,QAAA,CAASC,aAAa,CAAC;MAClCF,IAAA,CAAKN,EAAE,GAAGA,EAAA;MACVM,IAAA,CAAKG,KAAK,CAACC,OAAO,GAAG;MACrBJ,IAAA,CAAKK,WAAW,GAAGZ,WAAA;MACnBQ,QAAA,CAASK,IAAI,CAACC,WAAW,CAACP,IAAA;MAC1BF,IAAA,GAAO;QAACU,QAAA,EAAU;QAAGC,OAAA,EAAST;MAAI;MAClCV,sCAAA,CAAiBoB,GAAG,CAACjB,WAAA,EAAaK,IAAA;IACpC,OACEH,KAAA,CAAMG,IAAA,CAAKW,OAAO,CAACf,EAAE;IAGvBI,IAAA,CAAKU,QAAQ;IACb,OAAO;MACL,IAAIV,IAAA,IAAQ,EAAEA,IAAA,CAAKU,QAAQ,KAAK,GAAG;QACjCV,IAAA,CAAKW,OAAO,CAACE,MAAM;QACnBrB,sCAAA,CAAiBsB,MAAM,CAACnB,WAAA;MAC1B;IACF;EACF,GAAG,CAACA,WAAA,CAAY;EAEhB,OAAO;IACL,oBAAoBA,WAAA,GAAcC,EAAA,GAAKmB;EACzC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}