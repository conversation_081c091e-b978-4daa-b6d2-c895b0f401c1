{"ast": null, "code": "import { isElementVisible as $7d2416ea0959daaa$export$e989c0fffaa6b27a } from \"./isElementVisible.mjs\";\n\n/*\n * Copyright 2025 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nconst $b4b717babfbb907b$var$focusableElements = ['input:not([disabled]):not([type=hidden])', 'select:not([disabled])', 'textarea:not([disabled])', 'button:not([disabled])', 'a[href]', 'area[href]', 'summary', 'iframe', 'object', 'embed', 'audio[controls]', 'video[controls]', '[contenteditable]:not([contenteditable^=\"false\"])', 'permission'];\nconst $b4b717babfbb907b$var$FOCUSABLE_ELEMENT_SELECTOR = $b4b717babfbb907b$var$focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n$b4b717babfbb907b$var$focusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst $b4b717babfbb907b$var$TABBABLE_ELEMENT_SELECTOR = $b4b717babfbb907b$var$focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\nfunction $b4b717babfbb907b$export$4c063cf1350e6fed(element) {\n  return element.matches($b4b717babfbb907b$var$FOCUSABLE_ELEMENT_SELECTOR) && (0, $7d2416ea0959daaa$export$e989c0fffaa6b27a)(element) && !$b4b717babfbb907b$var$isInert(element);\n}\nfunction $b4b717babfbb907b$export$bebd5a1431fec25d(element) {\n  return element.matches($b4b717babfbb907b$var$TABBABLE_ELEMENT_SELECTOR) && (0, $7d2416ea0959daaa$export$e989c0fffaa6b27a)(element) && !$b4b717babfbb907b$var$isInert(element);\n}\nfunction $b4b717babfbb907b$var$isInert(element) {\n  let node = element;\n  while (node != null) {\n    if (node instanceof node.ownerDocument.defaultView.HTMLElement && node.inert) return true;\n    node = node.parentElement;\n  }\n  return false;\n}\nexport { $b4b717babfbb907b$export$4c063cf1350e6fed as isFocusable, $b4b717babfbb907b$export$bebd5a1431fec25d as isTabbable };", "map": {"version": 3, "names": ["$b4b717babfbb907b$var$focusableElements", "$b4b717babfbb907b$var$FOCUSABLE_ELEMENT_SELECTOR", "join", "push", "$b4b717babfbb907b$var$TABBABLE_ELEMENT_SELECTOR", "$b4b717babfbb907b$export$4c063cf1350e6fed", "element", "matches", "$7d2416ea0959daaa$export$e989c0fffaa6b27a", "$b4b717babfbb907b$var$isInert", "$b4b717babfbb907b$export$bebd5a1431fec25d", "node", "ownerDocument", "defaultView", "HTMLElement", "inert", "parentElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\faiasel\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\isFocusable.ts"], "sourcesContent": ["/*\n * Copyright 2025 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isElementVisible} from './isElementVisible';\n\nconst focusableElements = [\n  'input:not([disabled]):not([type=hidden])',\n  'select:not([disabled])',\n  'textarea:not([disabled])',\n  'button:not([disabled])',\n  'a[href]',\n  'area[href]',\n  'summary',\n  'iframe',\n  'object',\n  'embed',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable^=\"false\"])',\n  'permission'\n];\n\nconst FOCUSABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]),') + ',[tabindex]:not([disabled]):not([hidden])';\n\nfocusableElements.push('[tabindex]:not([tabindex=\"-1\"]):not([disabled])');\nconst TABBABLE_ELEMENT_SELECTOR = focusableElements.join(':not([hidden]):not([tabindex=\"-1\"]),');\n\nexport function isFocusable(element: Element): boolean {\n  return element.matches(FOCUSABLE_ELEMENT_SELECTOR) && isElementVisible(element) && !isInert(element);\n}\n\nexport function isTabbable(element: Element): boolean {\n  return element.matches(TABBABLE_ELEMENT_SELECTOR) && isElementVisible(element) && !isInert(element);\n}\n\nfunction isInert(element: Element): boolean {\n  let node: Element | null = element;\n  while (node != null) {\n    if (node instanceof node.ownerDocument.defaultView!.HTMLElement && node.inert) {\n      return true;\n    }\n\n    node = node.parentElement;\n  }\n\n  return false;\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAcA,MAAMA,uCAAA,GAAoB,CACxB,4CACA,0BACA,4BACA,0BACA,WACA,cACA,WACA,UACA,UACA,SACA,mBACA,mBACA,qDACA,aACD;AAED,MAAMC,gDAAA,GAA6BD,uCAAA,CAAkBE,IAAI,CAAC,qBAAqB;AAE/EF,uCAAA,CAAkBG,IAAI,CAAC;AACvB,MAAMC,+CAAA,GAA4BJ,uCAAA,CAAkBE,IAAI,CAAC;AAElD,SAASG,0CAAYC,OAAgB;EAC1C,OAAOA,OAAA,CAAQC,OAAO,CAACN,gDAAA,KAA+B,IAAAO,yCAAe,EAAEF,OAAA,KAAY,CAACG,6BAAA,CAAQH,OAAA;AAC9F;AAEO,SAASI,0CAAWJ,OAAgB;EACzC,OAAOA,OAAA,CAAQC,OAAO,CAACH,+CAAA,KAA8B,IAAAI,yCAAe,EAAEF,OAAA,KAAY,CAACG,6BAAA,CAAQH,OAAA;AAC7F;AAEA,SAASG,8BAAQH,OAAgB;EAC/B,IAAIK,IAAA,GAAuBL,OAAA;EAC3B,OAAOK,IAAA,IAAQ,MAAM;IACnB,IAAIA,IAAA,YAAgBA,IAAA,CAAKC,aAAa,CAACC,WAAW,CAAEC,WAAW,IAAIH,IAAA,CAAKI,KAAK,EAC3E,OAAO;IAGTJ,IAAA,GAAOA,IAAA,CAAKK,aAAa;EAC3B;EAEA,OAAO;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}