{"ast": null, "code": "import { isAndroid as $c87311424ea30a05$export$a11b0059900ceec8 } from \"./platform.mjs\";\n\n/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nfunction $6a7db85432448f7f$export$60278871457622de(event) {\n  // JAWS/NVDA with Firefox.\n  if (event.mozInputSource === 0 && event.isTrusted) return true;\n  // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n  // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n  // to detect TalkBack virtual clicks.\n  if ((0, $c87311424ea30a05$export$a11b0059900ceec8)() && event.pointerType) return event.type === 'click' && event.buttons === 1;\n  return event.detail === 0 && !event.pointerType;\n}\nfunction $6a7db85432448f7f$export$29bf1b5f2c56cf63(event) {\n  // If the pointer size is zero, then we assume it's from a screen reader.\n  // Android TalkBack double tap will sometimes return a event with width and height of 1\n  // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n  // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n  // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n  // Talkback double tap from Windows Firefox touch screen press\n  return !(0, $c87311424ea30a05$export$a11b0059900ceec8)() && event.width === 0 && event.height === 0 || event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse';\n}\nexport { $6a7db85432448f7f$export$60278871457622de as isVirtualClick, $6a7db85432448f7f$export$29bf1b5f2c56cf63 as isVirtualPointerEvent };", "map": {"version": 3, "names": ["$6a7db85432448f7f$export$60278871457622de", "event", "mozInputSource", "isTrusted", "$c87311424ea30a05$export$a11b0059900ceec8", "pointerType", "type", "buttons", "detail", "$6a7db85432448f7f$export$29bf1b5f2c56cf63", "width", "height", "pressure"], "sources": ["C:\\Users\\<USER>\\Desktop\\faiasel\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\isVirtualEvent.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {isAndroid} from './platform';\n\n// Original licensing for the following method can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/blob/3c713d513195a53788b3f8bb4b70279d68b15bcc/packages/react-interactions/events/src/dom/shared/index.js#L74-L87\n\n// Keyboards, Assistive Technologies, and element.click() all produce a \"virtual\"\n// click event. This is a method of inferring such clicks. Every browser except\n// IE 11 only sets a zero value of \"detail\" for click events that are \"virtual\".\n// However, IE 11 uses a zero value for all click events. For IE 11 we rely on\n// the quirk that it produces click events that are of type PointerEvent, and\n// where only the \"virtual\" click lacks a pointerType field.\n\nexport function isVirtualClick(event: MouseEvent | PointerEvent): boolean {\n  // JAWS/NVDA with Firefox.\n  if ((event as any).mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n\n  // Android TalkBack's detail value varies depending on the event listener providing the event so we have specific logic here instead\n  // If pointerType is defined, event is from a click listener. For events from mousedown listener, detail === 0 is a sufficient check\n  // to detect TalkBack virtual clicks.\n  if (isAndroid() && (event as PointerEvent).pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n\n  return event.detail === 0 && !(event as PointerEvent).pointerType;\n}\n\nexport function isVirtualPointerEvent(event: PointerEvent): boolean {\n  // If the pointer size is zero, then we assume it's from a screen reader.\n  // Android TalkBack double tap will sometimes return a event with width and height of 1\n  // and pointerType === 'mouse' so we need to check for a specific combination of event attributes.\n  // Cannot use \"event.pressure === 0\" as the sole check due to Safari pointer events always returning pressure === 0\n  // instead of .5, see https://bugs.webkit.org/show_bug.cgi?id=206216. event.pointerType === 'mouse' is to distingush\n  // Talkback double tap from Windows Firefox touch screen press\n  return (\n    (!isAndroid() && event.width === 0 && event.height === 0) ||\n    (event.width === 1 &&\n      event.height === 1 &&\n      event.pressure === 0 &&\n      event.detail === 0 &&\n      event.pointerType === 'mouse'\n    )\n  );\n}\n"], "mappings": ";;AAAA;;;;;;;;;;;AAyBO,SAASA,0CAAeC,KAAgC;EAC7D;EACA,IAAIA,KAAC,CAAcC,cAAc,KAAK,KAAKD,KAAA,CAAME,SAAS,EACxD,OAAO;EAGT;EACA;EACA;EACA,IAAI,IAAAC,yCAAQ,OAAOH,KAAC,CAAuBI,WAAW,EACpD,OAAOJ,KAAA,CAAMK,IAAI,KAAK,WAAWL,KAAA,CAAMM,OAAO,KAAK;EAGrD,OAAON,KAAA,CAAMO,MAAM,KAAK,KAAK,CAACP,KAAC,CAAuBI,WAAW;AACnE;AAEO,SAASI,0CAAsBR,KAAmB;EACvD;EACA;EACA;EACA;EACA;EACA;EACA,OACE,CAAE,IAAAG,yCAAQ,OAAOH,KAAA,CAAMS,KAAK,KAAK,KAAKT,KAAA,CAAMU,MAAM,KAAK,KACtDV,KAAA,CAAMS,KAAK,KAAK,KACfT,KAAA,CAAMU,MAAM,KAAK,KACjBV,KAAA,CAAMW,QAAQ,KAAK,KACnBX,KAAA,CAAMO,MAAM,KAAK,KACjBP,KAAA,CAAMI,WAAW,KAAK;AAG5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}