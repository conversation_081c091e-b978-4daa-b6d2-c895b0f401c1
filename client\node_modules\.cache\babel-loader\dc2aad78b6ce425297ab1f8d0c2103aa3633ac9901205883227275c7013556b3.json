{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */function $c87311424ea30a05$var$testUserAgent(re) {\n  var _window_navigator_userAgentData;\n  if (typeof window === 'undefined' || window.navigator == null) return false;\n  let brands = (_window_navigator_userAgentData = window.navigator['userAgentData']) === null || _window_navigator_userAgentData === void 0 ? void 0 : _window_navigator_userAgentData.brands;\n  return Array.isArray(brands) && brands.some(brand => re.test(brand.brand)) || re.test(window.navigator.userAgent);\n}\nfunction $c87311424ea30a05$var$testPlatform(re) {\n  var _window_navigator_userAgentData;\n  return typeof window !== 'undefined' && window.navigator != null ? re.test(((_window_navigator_userAgentData = window.navigator['userAgentData']) === null || _window_navigator_userAgentData === void 0 ? void 0 : _window_navigator_userAgentData.platform) || window.navigator.platform) : false;\n}\nfunction $c87311424ea30a05$var$cached(fn) {\n  if (process.env.NODE_ENV === 'test') return fn;\n  let res = null;\n  return () => {\n    if (res == null) res = fn();\n    return res;\n  };\n}\nconst $c87311424ea30a05$export$9ac100e40613ea10 = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$var$testPlatform(/^Mac/i);\n});\nconst $c87311424ea30a05$export$186c6964ca17d99 = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$var$testPlatform(/^iPhone/i);\n});\nconst $c87311424ea30a05$export$7bef049ce92e4224 = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$var$testPlatform(/^iPad/i) ||\n  // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n  $c87311424ea30a05$export$9ac100e40613ea10() && navigator.maxTouchPoints > 1;\n});\nconst $c87311424ea30a05$export$fedb369cb70207f1 = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$export$186c6964ca17d99() || $c87311424ea30a05$export$7bef049ce92e4224();\n});\nconst $c87311424ea30a05$export$e1865c3bedcd822b = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$export$9ac100e40613ea10() || $c87311424ea30a05$export$fedb369cb70207f1();\n});\nconst $c87311424ea30a05$export$78551043582a6a98 = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$var$testUserAgent(/AppleWebKit/i) && !$c87311424ea30a05$export$6446a186d09e379e();\n});\nconst $c87311424ea30a05$export$6446a186d09e379e = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$var$testUserAgent(/Chrome/i);\n});\nconst $c87311424ea30a05$export$a11b0059900ceec8 = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$var$testUserAgent(/Android/i);\n});\nconst $c87311424ea30a05$export$b7d78993b74f766d = $c87311424ea30a05$var$cached(function () {\n  return $c87311424ea30a05$var$testUserAgent(/Firefox/i);\n});\nexport { $c87311424ea30a05$export$9ac100e40613ea10 as isMac, $c87311424ea30a05$export$186c6964ca17d99 as isIPhone, $c87311424ea30a05$export$7bef049ce92e4224 as isIPad, $c87311424ea30a05$export$fedb369cb70207f1 as isIOS, $c87311424ea30a05$export$e1865c3bedcd822b as isAppleDevice, $c87311424ea30a05$export$78551043582a6a98 as isWebKit, $c87311424ea30a05$export$6446a186d09e379e as isChrome, $c87311424ea30a05$export$a11b0059900ceec8 as isAndroid, $c87311424ea30a05$export$b7d78993b74f766d as isFirefox };", "map": {"version": 3, "names": ["$c87311424ea30a05$var$testUserAgent", "re", "_window_navigator_userAgentData", "window", "navigator", "brands", "Array", "isArray", "some", "brand", "test", "userAgent", "$c87311424ea30a05$var$testPlatform", "platform", "$c87311424ea30a05$var$cached", "fn", "process", "env", "NODE_ENV", "res", "$c87311424ea30a05$export$9ac100e40613ea10", "$c87311424ea30a05$export$186c6964ca17d99", "$c87311424ea30a05$export$7bef049ce92e4224", "maxTouchPoints", "$c87311424ea30a05$export$fedb369cb70207f1", "$c87311424ea30a05$export$e1865c3bedcd822b", "$c87311424ea30a05$export$78551043582a6a98", "$c87311424ea30a05$export$6446a186d09e379e", "$c87311424ea30a05$export$a11b0059900ceec8", "$c87311424ea30a05$export$b7d78993b74f766d"], "sources": ["C:\\Users\\<USER>\\Desktop\\faiasel\\client\\node_modules\\@react-aria\\utils\\dist\\packages\\@react-aria\\utils\\src\\platform.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction testUserAgent(re: RegExp) {\n  if (typeof window === 'undefined' || window.navigator == null) {\n    return false;\n  }\n  let brands = window.navigator['userAgentData']?.brands;\n  return Array.isArray(brands) && brands.some((brand: {brand: string, version: string}) => re.test(brand.brand)) ||\n    re.test(window.navigator.userAgent);\n}\n\nfunction testPlatform(re: RegExp) {\n  return typeof window !== 'undefined' && window.navigator != null\n    ? re.test(window.navigator['userAgentData']?.platform || window.navigator.platform)\n    : false;\n}\n\nfunction cached(fn: () => boolean) {\n  if (process.env.NODE_ENV === 'test') {\n    return fn;\n  }\n\n  let res: boolean | null = null;\n  return () => {\n    if (res == null) {\n      res = fn();\n    }\n    return res;\n  };\n}\n\nexport const isMac: () => boolean = cached(function () {\n  return testPlatform(/^Mac/i);\n});\n\nexport const isIPhone: () => boolean = cached(function () {\n  return testPlatform(/^iPhone/i);\n});\n\nexport const isIPad: () => boolean = cached(function () {\n  return testPlatform(/^iPad/i) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1);\n});\n\nexport const isIOS: () => boolean = cached(function () {\n  return isIPhone() || isIPad();\n});\n\nexport const isAppleDevice: () => boolean = cached(function () {\n  return isMac() || isIOS();\n});\n\nexport const isWebKit: () => boolean = cached(function () {\n  return testUserAgent(/AppleWebKit/i) && !isChrome();\n});\n\nexport const isChrome: () => boolean = cached(function () {\n  return testUserAgent(/Chrome/i);\n});\n\nexport const isAndroid: () => boolean = cached(function () {\n  return testUserAgent(/Android/i);\n});\n\nexport const isFirefox: () => boolean = cached(function () {\n  return testUserAgent(/Firefox/i);\n});\n"], "mappings": "AAAA;;;;;;;;;;GAYA,SAASA,oCAAcC,EAAU;MAIlBC,+BAAA;EAHb,IAAI,OAAOC,MAAA,KAAW,eAAeA,MAAA,CAAOC,SAAS,IAAI,MACvD,OAAO;EAET,IAAIC,MAAA,IAASH,+BAAA,GAAAC,MAAA,CAAOC,SAAS,CAAC,gBAAgB,cAAjCF,+BAAA,uBAAAA,+BAAA,CAAmCG,MAAM;EACtD,OAAOC,KAAA,CAAMC,OAAO,CAACF,MAAA,KAAWA,MAAA,CAAOG,IAAI,CAAEC,KAAA,IAA4CR,EAAA,CAAGS,IAAI,CAACD,KAAA,CAAMA,KAAK,MAC1GR,EAAA,CAAGS,IAAI,CAACP,MAAA,CAAOC,SAAS,CAACO,SAAS;AACtC;AAEA,SAASC,mCAAaX,EAAU;MAElBC,+BAAA;EADZ,OAAO,OAAOC,MAAA,KAAW,eAAeA,MAAA,CAAOC,SAAS,IAAI,OACxDH,EAAA,CAAGS,IAAI,CAAC,EAAAR,+BAAA,GAAAC,MAAA,CAAOC,SAAS,CAAC,gBAAgB,cAAjCF,+BAAA,uBAAAA,+BAAA,CAAmCW,QAAQ,KAAIV,MAAA,CAAOC,SAAS,CAACS,QAAQ,IAChF;AACN;AAEA,SAASC,6BAAOC,EAAiB;EAC/B,IAAIC,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,QAC3B,OAAOH,EAAA;EAGT,IAAII,GAAA,GAAsB;EAC1B,OAAO;IACL,IAAIA,GAAA,IAAO,MACTA,GAAA,GAAMJ,EAAA;IAER,OAAOI,GAAA;EACT;AACF;AAEO,MAAMC,yCAAA,GAAuBN,4BAAA,CAAO;EACzC,OAAOF,kCAAA,CAAa;AACtB;AAEO,MAAMS,wCAAA,GAA0BP,4BAAA,CAAO;EAC5C,OAAOF,kCAAA,CAAa;AACtB;AAEO,MAAMU,yCAAA,GAAwBR,4BAAA,CAAO;EAC1C,OAAOF,kCAAA,CAAa;EAClB;EACCQ,yCAAA,MAAWhB,SAAA,CAAUmB,cAAc,GAAG;AAC3C;AAEO,MAAMC,yCAAA,GAAuBV,4BAAA,CAAO;EACzC,OAAOO,wCAAA,MAAcC,yCAAA;AACvB;AAEO,MAAMG,yCAAA,GAA+BX,4BAAA,CAAO;EACjD,OAAOM,yCAAA,MAAWI,yCAAA;AACpB;AAEO,MAAME,yCAAA,GAA0BZ,4BAAA,CAAO;EAC5C,OAAOd,mCAAA,CAAc,mBAAmB,CAAC2B,yCAAA;AAC3C;AAEO,MAAMA,yCAAA,GAA0Bb,4BAAA,CAAO;EAC5C,OAAOd,mCAAA,CAAc;AACvB;AAEO,MAAM4B,yCAAA,GAA2Bd,4BAAA,CAAO;EAC7C,OAAOd,mCAAA,CAAc;AACvB;AAEO,MAAM6B,yCAAA,GAA2Bf,4BAAA,CAAO;EAC7C,OAAOd,mCAAA,CAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}