[{"C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\Layout.js": "4", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\context\\AuthContext.js": "5", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\pages\\NotFound.js": "6", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\pages\\Home.js": "7", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\Footer.js": "8", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\Header.js": "9", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\LoadingSpinner.js": "10", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\ErrorMessage.js": "11", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\utils\\helpers.js": "12", "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\utils\\api.js": "13"}, {"size": 535, "mtime": 1754995253483, "results": "14", "hashOfConfig": "15"}, {"size": 362, "mtime": 1754995254122, "results": "16", "hashOfConfig": "15"}, {"size": 3322, "mtime": 1754996211673, "results": "17", "hashOfConfig": "15"}, {"size": 336, "mtime": 1754996165216, "results": "18", "hashOfConfig": "15"}, {"size": 5822, "mtime": 1754996108951, "results": "19", "hashOfConfig": "15"}, {"size": 996, "mtime": 1754996261443, "results": "20", "hashOfConfig": "15"}, {"size": 8645, "mtime": 1754996251137, "results": "21", "hashOfConfig": "15"}, {"size": 6018, "mtime": 1754996158357, "results": "22", "hashOfConfig": "15"}, {"size": 5121, "mtime": 1754996131822, "results": "23", "hashOfConfig": "15"}, {"size": 467, "mtime": 1754996172371, "results": "24", "hashOfConfig": "15"}, {"size": 1847, "mtime": 1754996184293, "results": "25", "hashOfConfig": "15"}, {"size": 5567, "mtime": 1754996084235, "results": "26", "hashOfConfig": "15"}, {"size": 5079, "mtime": 1754996058188, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "uy6afy", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\components\\ErrorMessage.js", [], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\utils\\helpers.js", ["67", "68", "69"], [], "C:\\Users\\<USER>\\Desktop\\faiasel\\client\\src\\utils\\api.js", [], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 48, "column": 25, "nodeType": "72", "messageId": "73", "endLine": 48, "endColumn": 26, "suggestions": "74"}, {"ruleId": "70", "severity": 1, "message": "75", "line": 49, "column": 46, "nodeType": "72", "messageId": "73", "endLine": 49, "endColumn": 47, "suggestions": "76"}, {"ruleId": "70", "severity": 1, "message": "77", "line": 49, "column": 48, "nodeType": "72", "messageId": "73", "endLine": 49, "endColumn": 49, "suggestions": "78"}, "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["79", "80"], "Unnecessary escape character: \\(.", ["81", "82"], "Unnecessary escape character: \\).", ["83", "84"], {"messageId": "85", "fix": "86", "desc": "87"}, {"messageId": "88", "fix": "89", "desc": "90"}, {"messageId": "85", "fix": "91", "desc": "87"}, {"messageId": "88", "fix": "92", "desc": "90"}, {"messageId": "85", "fix": "93", "desc": "87"}, {"messageId": "88", "fix": "94", "desc": "90"}, "removeEscape", {"range": "95", "text": "96"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "97", "text": "98"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "99", "text": "96"}, {"range": "100", "text": "98"}, {"range": "101", "text": "96"}, {"range": "102", "text": "98"}, [1227, 1228], "", [1227, 1227], "\\", [1295, 1296], [1295, 1295], [1297, 1298], [1297, 1297]]